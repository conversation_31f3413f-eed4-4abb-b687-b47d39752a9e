<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3">
      <div class="row p-3 items-center">
        <q-card class="col">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Total de Acessos: {{total}}</div>
        </q-card-section>
      </q-card>
      <div class="q-ml-md" v-if="isColaborador">
        <q-btn
          rounded
          color="secondary"
          icon="delivery_dining"
          label="Entregar Análise"
          @click="onClickEntregarAnalise"
        />
      </div>
      </div>

      <!-- Listagem de Entregas -->
      <div class="p-3" v-if="isColaborador">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">📦 Entregas Realizadas</div>

            <q-table
              :rows="entregas"
              :columns="[
                { name: 'code', label: 'Código', field: 'code', align: 'left', sortable: true },
                { name: 'customer_name', label: 'Cliente', field: 'customer_name', align: 'left', sortable: true },
                { name: 'status', label: 'Status', field: 'unlocked', align: 'center' },
                { name: 'created', label: 'Data de Criação', field: (row) => formatDate(row.xata.createdAt), align: 'left', sortable: true },
                { name: 'actions', label: 'Ações', align: 'center' }
              ]"
              row-key="id"
              :pagination="{ rowsPerPage: 10 }"
              flat
              bordered
            >
              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-badge
                    :color="props.row.unlocked ? 'green' : 'orange'"
                    :label="props.row.unlocked ? 'Desbloqueado' : 'Bloqueado'"
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <q-btn
                    flat
                    round
                    color="primary"
                    icon="content_copy"
                    size="sm"
                    @click="copiarCodigoTabela(props.row.code)"
                  >
                    <q-tooltip>Copiar código</q-tooltip>
                  </q-btn>
                  <q-btn
                    flat
                    round
                    color="secondary"
                    icon="open_in_new"
                    size="sm"
                    @click="abrirLink(props.row.url)"
                  >
                    <q-tooltip>Abrir link</q-tooltip>
                  </q-btn>
                </q-td>
              </template>

              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey q-gutter-sm">
                  <q-icon size="2em" name="inbox" />
                  <span>Nenhuma entrega encontrada</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>

      <!-- Mapa de Acessos -->
      <div class="p-3">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">🗺️ Mapa de Acessos</div>
            <GMapMap :center="center" :zoom="6" map-type-id="terrain" class="h-screen">
              <GMapCluster>
                <GMapMarker
                  :key="index"
                  v-for="(m, index) in markers"
                  :position="m.position"
                  :clickable="true"
                  :draggable="false"
                  @click="center = m.position"
                />
              </GMapCluster>
              <GMapCluster :zoomOnClick="true">

              </GMapCluster>
            </GMapMap>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Modal de Entrega -->
    <q-dialog v-model="dialogEntrega" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">Entregar Análise</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit.prevent="onSubmitEntrega" class="q-gutter-md">
            <q-input
              v-model="entregaData.url"
              label="Link da Entrega *"
              outlined
              dense
              :rules="[val => !!val || 'Link é obrigatório', val => isValidUrl(val) || 'URL inválida']"
            >
              <template v-slot:prepend>
                <q-icon name="link" />
              </template>
            </q-input>

            <q-input
              v-model="entregaData.customer_name"
              label="Nome do Cliente *"
              outlined
              dense
              :rules="[val => !!val || 'Nome do cliente é obrigatório']"
            >
              <template v-slot:prepend>
                <q-icon name="person" />
              </template>
            </q-input>
          </q-form>
        </q-card-section>

        <q-card-actions align="right" class="text-primary">
          <q-btn flat label="Cancelar" @click="dialogEntrega = false" />
          <q-btn flat label="Entregar" @click="onSubmitEntrega" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Modal do Código de Entrega -->
    <q-dialog v-model="dialogCodigo" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6 text-center">🎉 Entrega Realizada!</div>
        </q-card-section>

        <q-card-section class="q-pt-none text-center">
          <p class="text-body1 q-mb-md">
            Sua análise foi entregue com sucesso! Envie este código para o cliente:
          </p>

          <q-card class="bg-grey-2 q-pa-md">
            <div class="text-h4 text-primary text-weight-bold text-center">
              {{ codigoEntrega }}
            </div>
          </q-card>

          <p class="text-caption q-mt-md text-grey-7">
            O cliente poderá usar este código para acessar a análise
          </p>
        </q-card-section>

        <q-card-actions align="center" class="q-pb-md">
          <q-btn
            color="primary"
            icon="content_copy"
            label="Copiar Código"
            @click="copiarCodigo"
            class="q-mr-sm"
          />
          <q-btn
            flat
            label="Fechar"
            @click="dialogCodigo = false"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts">
// import QRious from 'qrious';
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as UserService from 'src/services/UserService';
import * as EntregaService from 'src/services/EntregaService';
import type { Entrega } from 'src/services/EntregaService';
import { useQuasar } from 'quasar';
import { triggerSuccess, triggerNegative } from 'src/utils/triggers';
import axios from 'axios';

export default defineComponent({
  name: 'DashboardPage',
  components: {  },
  setup () {
    const $q = useQuasar();
    const user = useUserStore();
    const User = ref<UserType>()
    const isColaborador = ref(false);
    const dialogEntrega = ref(false);
    const dialogCodigo = ref(false);
    const codigoEntrega = ref('');
    const entregaData = ref({
      url: '',
      customer_name: ''
    });
    const entregas = ref<Entrega[]>([]);

    const markers = ref([
      {
        position: {
          lat: -28.366136250673932,
          lng:  -54.266992761911396,
        },
      }
    ])
    const center = { lat: -28.366136250673932, lng: -54.266992761911396 };

    const total = ref(0)

    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
      isColaborador.value = newValue && (newValue.type === 'A' || newValue.type === 'C')
    })

    const onClickEntregarAnalise = () => {
      dialogEntrega.value = true;
    }

    const isValidUrl = (url: string) => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    }

    const onSubmitEntrega = async () => {
      if (!entregaData.value.url || !entregaData.value.customer_name) {
        triggerNegative('Todos os campos são obrigatórios');
        return;
      }

      if (!isValidUrl(entregaData.value.url)) {
        triggerNegative('URL inválida');
        return;
      }

      $q.loading.show({ message: 'Realizando entrega...' });

      try {
        const response = await EntregaService.Entregar(entregaData.value);

        if (axios.isAxiosError(response)) {
          triggerNegative(response.response?.data?.message || 'Erro ao realizar entrega');
        } else {
          triggerSuccess('Entrega realizada com sucesso!');

          // Capturar o código da resposta e exibir
          if (response.data && response.data.code) {
            codigoEntrega.value = response.data.code;
            dialogCodigo.value = true;
          }

          dialogEntrega.value = false;
          entregaData.value = { url: '', customer_name: '' };

          // Recarregar a lista de entregas
          await buscarEntregas();
        }
      } catch (error) {
        console.error('Erro na entrega:', error);
        triggerNegative('Erro ao realizar entrega');
      } finally {
        $q.loading.hide();
      }
    }

    const copiarCodigo = async () => {
      try {
        await navigator.clipboard.writeText(codigoEntrega.value);
        triggerSuccess('Código copiado para a área de transferência!');
      } catch (error) {
        triggerNegative('Erro ao copiar código');
      }
    }

    const buscarEntregas = async () => {
      try {
        const response = await EntregaService.Get();
        if (response.data) {
          entregas.value = response.data;
        }
      } catch (error) {
        console.error('Erro ao buscar entregas:', error);
        triggerNegative('Erro ao carregar entregas');
      }
    }

    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    const copiarCodigoTabela = async (codigo: string) => {
      try {
        await navigator.clipboard.writeText(codigo);
        triggerSuccess(`Código ${codigo} copiado!`);
      } catch (error) {
        triggerNegative('Erro ao copiar código');
      }
    }

    const abrirLink = (url: string) => {
      window.open(url, '_blank');
    }

    onMounted(async () => {
      User.value = await user.storeUserDataGetter;
      isColaborador.value = User.value && (User.value.type === 'A' || User.value.type === 'C');

      // Carregar entregas se for colaborador
      if (isColaborador.value) {
        await buscarEntregas();
      }

      const responseAcessos = await UserService.GetUser('acessos')
      const markersAux = []
      if (responseAcessos.data.success === true){
        for (let acesso of responseAcessos.data.acessos){
          let found = markersAux.find((marker) => marker.position.lat === acesso.latitude && marker.position.lng === acesso.longitude)
          total.value += acesso.quantidade
          if (!found) {
            markersAux.push({
                position: {
                  lat: acesso.latitude,
                  lng: acesso.longitude,
                }
            })
          }
        }
      }

      markers.value = markersAux
    })

    return {
      User,
      markers,
      total,
      center,
      isColaborador,
      dialogEntrega,
      dialogCodigo,
      codigoEntrega,
      entregaData,
      entregas,
      onClickEntregarAnalise,
      isValidUrl,
      onSubmitEntrega,
      copiarCodigo,
      buscarEntregas,
      formatDate,
      copiarCodigoTabela,
      abrirLink,
     };


  }
});
</script>

<style scoped>

</style>